# Gift Manager Setup Guide

The Gift Manager is an automated system that allows the marketplace bot to receive Telegram Star Gifts and automatically return them to the sender. This creates a seamless gift exchange experience.

## How It Works

1. **User Account Integration**: The bot connects to a real Telegram user account using the Telegram Client API (GramJS)
2. **Gift Detection**: When someone sends a Star Gift to the connected user account, the system detects it automatically
3. **Automatic Return**: The system immediately sends a gift back to the sender with a thank you message
4. **Fallback Handling**: If gift sending fails, it sends a thank you message instead

## Prerequisites

Before setting up the Gift Manager, you need:

1. **Telegram API Credentials**:
   - API ID and API Hash from https://my.telegram.org/apps
   - A dedicated Telegram user account for receiving gifts

2. **Sufficient Stars**: The user account needs to have enough Telegram Stars to send gifts back

3. **Node.js Dependencies**: The `telegram` package (GramJS) is required

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment Variables

Add these variables to your `.env` file:

```env
# Telegram User Account Configuration (for gift handling)
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here
TELEGRAM_SESSION_STRING=
TELEGRAM_PHONE_NUMBER=+**********
```

### 3. Get API Credentials

1. Go to https://my.telegram.org/apps
2. Log in with your phone number
3. Create a new application
4. Copy the `api_id` and `api_hash`
5. Add them to your `.env` file

### 4. Set Up User Account Authentication

Run the setup script to authenticate your Telegram user account:

```bash
npm run setup-telegram
```

This script will:
- Prompt you for your phone number
- Send you a verification code
- Ask for your 2FA password (if enabled)
- Generate a session string
- Test the connection by sending a message to yourself

**Important**: Copy the generated session string to your `.env` file as `TELEGRAM_SESSION_STRING`.

### 5. Start the Bot

```bash
npm run dev
```

### 6. Initialize Gift Manager

In Telegram, send these commands to your bot:

```
/gift_help    # View detailed help
/gift_start   # Start the gift manager
/gift_status  # Check if it's running properly
```

## Bot Commands

### Gift Manager Commands

- `/gift_start` - Start the automatic gift return system
- `/gift_stop` - Stop the automatic gift return system
- `/gift_status` - Show current status and statistics
- `/gift_recent` - Show recent received gifts (last 10)
- `/gift_reconnect` - Reconnect to Telegram user account
- `/gift_help` - Show detailed gift manager help

### Status Information

The status command shows:
- Connection status (Connected/Disconnected)
- Total gifts received
- Total gifts returned
- Total failed returns
- Success rate percentage
- Last gift received date

## How to Test

1. **Start the Gift Manager**: Send `/gift_start` to your bot
2. **Check Status**: Send `/gift_status` to verify it's running
3. **Send a Test Gift**: From another account, send a Star Gift to your user account
4. **Verify Return**: Check that a gift was automatically sent back
5. **Monitor Logs**: Watch the console for gift activity logs

## Troubleshooting

### Common Issues

1. **"Client not connected" errors**:
   - Check your session string is correct
   - Verify API credentials
   - Try `/gift_reconnect`

2. **"Failed to return gift" errors**:
   - Ensure the user account has sufficient Stars
   - Check if the gift type is available for purchase
   - Verify network connectivity

3. **Authentication failures**:
   - Re-run `npm run setup-telegram`
   - Check phone number format (+country_code_number)
   - Verify 2FA password if enabled

### Debug Steps

1. **Check Environment Variables**:
   ```bash
   echo $TELEGRAM_API_ID
   echo $TELEGRAM_API_HASH
   # Session string should be set but don't echo it for security
   ```

2. **Verify Bot Logs**:
   - Look for "Connected to Telegram user account successfully"
   - Check for any error messages during gift handling

3. **Test Connection**:
   - Use `/gift_status` to check connection status
   - Try `/gift_reconnect` if disconnected

## Security Considerations

1. **Session String Security**:
   - Never share your session string
   - Don't commit it to version control
   - Store it securely in environment variables

2. **Account Safety**:
   - Use a dedicated account for gift handling
   - Don't use your personal Telegram account
   - Monitor account activity regularly

3. **Star Management**:
   - Keep track of Star balance
   - Set up alerts for low balance
   - Consider automatic top-up mechanisms

## Architecture

The Gift Manager consists of:

1. **TelegramUserClient** (`src/services/telegram-user-client.ts`):
   - Handles connection to Telegram user account
   - Listens for incoming gift messages
   - Manages gift return logic

2. **GiftManager** (`src/services/gift-manager.ts`):
   - Coordinates gift handling operations
   - Tracks statistics and status
   - Provides admin interface

3. **Gift Commands** (`src/handlers/gift-commands.ts`):
   - Bot command handlers for gift management
   - Admin interface for monitoring and control

## Monitoring and Maintenance

### Regular Checks

1. **Daily**: Check gift manager status
2. **Weekly**: Review gift statistics and success rates
3. **Monthly**: Verify Star balance and top up if needed

### Log Monitoring

Watch for these log messages:
- "Connected to Telegram user account successfully"
- "Received a star gift!"
- "Successfully returned gift to user"
- "Failed to return gift" (investigate these)

### Performance Metrics

Track these metrics:
- Gift return success rate (should be >90%)
- Average response time for gift returns
- Connection uptime percentage
- Star balance trends

## Support

If you encounter issues:

1. Check this documentation first
2. Review bot logs for error messages
3. Try the troubleshooting steps above
4. Contact the development team with:
   - Error messages from logs
   - Steps to reproduce the issue
   - Current configuration (without sensitive data)
