{"name": "marketplace-bot", "version": "1.0.0", "description": "Telegram bot for the marketplace platform", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "setup-telegram": "npx ts-node src/setup-telegram-account.ts", "lint": "echo \"Add ESLint configuration if needed\"", "test": "echo \"Add tests when needed\""}, "keywords": ["telegram", "bot", "marketplace", "typescript"], "author": "Marketplace Team", "license": "ISC", "type": "commonjs", "dependencies": {"@telegram-apps/sdk-react": "^3.3.1", "axios": "^1.10.0", "dotenv": "^16.5.0", "eruda": "^3.4.3", "telegraf": "^4.16.3", "telegram": "^2.25.15"}, "devDependencies": {"@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}