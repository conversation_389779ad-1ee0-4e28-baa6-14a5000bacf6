import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

bot.on("message", async (ctx) => {
  // Check if message contains a gift
  if (ctx.message && "gift" in ctx.message) {
    try {
      const gift = (ctx.message as any).gift;
      const senderId = ctx.from?.id;

      console.log("Gift received:", {
        giftId: gift?.id,
        senderId,
        senderName: ctx.from?.first_name,
        date: new Date(),
      });

      if (senderId && gift?.id) {
        // Use axios to call the transferGift API directly
        const response = await fetch(
          `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              business_connection_id: process.env.BUSINESS_CONNECTION_ID,
              owned_gift_id: gift.id,
              new_owner_chat_id: senderId,
            }),
          }
        );

        const result = (await response.json()) as {
          ok: boolean;
          description?: string;
        };

        if (result.ok) {
          console.log(`Gift echoed back to user ${senderId}`);
        } else {
          console.error("Failed to transfer gift:", result);
        }
      }
    } catch (error) {
      console.error("Error handling gift:", error);
    }
  } else {
    // Pass to regular message handler
    await handleMessage(ctx);
  }
});

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx.reply("Sorry, something went wrong. Please try again later.");
});

export default bot;
