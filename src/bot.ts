import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

const GIFT_ID = "5170594532177215681";

const SENDER_ID = "*********";

console.log(GIFT_ID);

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.use(async (ctx, next) => {
  try {
    if (ctx.update && "business_connection" in ctx.update) {
      const businessConnection = (ctx.update as any).business_connection;
      const businessAccountId = businessConnection.user_chat_id;

      console.log(`Business Account Connected: ID = ${businessAccountId}`);

      await ctx.telegram.sendMessage(
        businessAccountId,
        "Thanks for connecting me to your Business account!"
      );
      return;
    }

    if (ctx.update && "business_message" in ctx.update) {
      const businessMessage = (ctx.update as any).business_message;
      const businessConnectionId = businessMessage.business_connection_id;
      const senderId = businessMessage.from?.id;

      // console.log("businessMessage", businessMessage);

      // Check if this business message contains a gift
      if (businessMessage && "gift" in businessMessage) {
        const gift = businessMessage.gift;

        console.log("Gift received in business account:", {
          giftId: gift?.id,
          senderId,
          senderName: businessMessage.from?.first_name,
          businessConnectionId,
          date: new Date(),
        });

        if (senderId && gift?.id && businessConnectionId) {
          try {
            // Transfer the gift back to the sender using transferGift API
            const response = await fetch(
              `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  business_connection_id: businessConnectionId,
                  owned_gift_id: gift.id,
                  new_owner_chat_id: senderId,
                }),
              }
            );

            const result = (await response.json()) as {
              ok: boolean;
              description?: string;
            };

            if (result.ok) {
              console.log(`Gift echoed back to user ${senderId}`);
            } else {
              console.error("Failed to transfer gift:", result);
            }
          } catch (error) {
            console.error("Failed to transfer gift:", error);
          }
        }
        return; // Don't process gift messages further
      }

      const response = await fetch(
        `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            business_connection_id: businessConnectionId,
            owned_gift_id: GIFT_ID,
            new_owner_chat_id: SENDER_ID,
          }),
        }
      );

      const result = (await response.json()) as {
        ok: boolean;
        description?: string;
      };

      console.log("result", result);

      // Handle regular business messages (non-gifts)
      console.log("Regular business message received:", {
        senderId,
        senderName: businessMessage.from?.first_name,
        text: businessMessage.text,
        businessConnectionId,
      });
      return;
    }

    // Continue to next middleware for regular updates
    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Handle regular messages
bot.on("message", handleMessage);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  if (ctx && ctx.reply) {
    ctx.reply("Sorry, something went wrong. Please try again later.");
  }
});

// Graceful shutdown handlers
process.once("SIGINT", () => {
  console.log("Received SIGINT, stopping bot...");
  bot.stop("SIGINT");
});

process.once("SIGTERM", () => {
  console.log("Received SIGTERM, stopping bot...");
  bot.stop("SIGTERM");
});

export default bot;
