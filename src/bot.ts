import dotenv from "dotenv";
import { Telegraf } from "telegraf";
import {
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetMyOrdersButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleBackToMenuCallback,
  handleBackToOrdersCallback,
  handleCancelEchoModeCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleOrderCompletionCallback,
  handleOrderHelpCallback,
  handleOrderSelectionCallback,
} from "./handlers/callbacks";
import { handleHelpCommand, handleStartCommand } from "./handlers/commands";
import { handleMessage } from "./handlers/messages";

dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const bot = new Telegraf(BOT_TOKEN);

bot.start(handleStartCommand);
bot.help(handleHelpCommand);

bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Handle all updates to catch business messages
bot.use(async (ctx, next) => {
  try {
    // Check if this is a business message update with a gift
    if (ctx.update && "business_message" in ctx.update) {
      const message = (ctx.update as any).business_message;

      if (message && typeof message === "object" && "gift" in message) {
        const gift = (message as any).gift;
        const senderId = (message as any).from?.id;
        const businessConnectionId = (message as any).business_connection_id;

        console.log("Gift received in business account:", {
          giftId: gift?.id,
          senderId,
          senderName: (message as any).from?.first_name,
          businessConnectionId,
          date: new Date(),
        });

        if (senderId && gift?.id && businessConnectionId) {
          // Transfer the gift back to the sender
          const response = await fetch(
            `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                business_connection_id: businessConnectionId,
                owned_gift_id: gift.id,
                new_owner_chat_id: senderId,
              }),
            }
          );

          const result = (await response.json()) as {
            ok: boolean;
            description?: string;
          };

          if (result.ok) {
            console.log(`Gift echoed back to user ${senderId}`);
          } else {
            console.error("Failed to transfer gift:", result);
          }
        }

        // Don't continue to next middleware for gift messages
        return;
      }
    }

    // Continue to next middleware for non-gift messages
    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});

// Handle regular messages
bot.on("message", handleMessage);

bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx.reply("Sorry, something went wrong. Please try again later.");
});

export default bot;
