"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Minus, Plus, User, Lock } from "lucide-react";
import Image from "next/image";
import { useTonConnectUI } from "@tonconnect/ui-react";
import { Address } from "@ton/core";
import { useTelegramAuth } from "@/hooks/useTelegramAuth";
import { useRootContext } from "@/root-context";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

// Constants
const BUTTON_CLASSES = {
  actionButton:
    "w-6 h-6 rounded-full bg-ton-main hover:bg-ton-main/80 transition-all ease-in-out",
  walletButton:
    "bg-ton-main hover:bg-ton-main hover:scale-110 transition-all ease-in-out text-white rounded-full text-xs font-semibold py-1 px-2 flex-shrink-0 min-w-0",
};

const TON_LOGO_PROPS = {
  src: "/ton.svg",
  alt: "TON Logo",
};

// Reusable TON Logo component
const TonLogo = ({
  size = 32,
  className = "",
}: {
  size?: number;
  className?: string;
}) => (
  <Image {...TON_LOGO_PROPS} width={size} height={size} className={className} />
);

export default function MarketplaceHeader() {
  const router = useRouter();
  const [tonConnectUI] = useTonConnectUI();
  const [tonWalletAddress, setTonWalletAddress] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState(false);

  const { currentUser } = useRootContext();

  const { authenticate, isLoading } = useTelegramAuth({
    onSuccess: () => {
      toast.success("Authenticated successfully");
    },
    onError: (error) => {
      toast.error(`Authentication failed: ${error}`);
    },
  });

  // Computed values using useMemo for performance
  const balanceInfo = useMemo(() => {
    if (!currentUser?.balance) {
      return { available: "354.00", locked: 50, hasLocked: true };
    }

    const available = (
      currentUser.balance.sum - currentUser.balance.locked
    ).toFixed(2);
    const locked = currentUser.balance.locked;
    const hasLocked = locked > 0;

    return { available, locked, hasLocked };
  }, [currentUser?.balance]);

  const onProfileButtonClick = () => {
    if (currentUser) {
      router.push("/profile");
    } else {
      authenticate();
    }
  };

  const handleWalletConnection = useCallback(async (address: string) => {
    try {
      setTonWalletAddress(address);
    } catch (error) {
      console.error("Error connecting wallet:", error);
    } finally {
      setIsConnecting(false);
    }
  }, []);

  const handleWalletDisconnection = useCallback(async () => {
    setTonWalletAddress("");
  }, []);

  useEffect(() => {
    const unsubscribe = tonConnectUI.onStatusChange(async (wallet) => {
      if (wallet && isConnecting) {
        await handleWalletConnection(wallet.account.address);
      } else if (!wallet && !isConnecting) {
        await handleWalletDisconnection();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [
    tonConnectUI,
    handleWalletConnection,
    handleWalletDisconnection,
    isConnecting,
  ]);

  const handleWalletAction = async () => {
    if (tonConnectUI.account?.address) {
      await tonConnectUI.disconnect();
    } else {
      setIsConnecting(true);
      await tonConnectUI.openModal();
    }
  };

  const formatAddress = (address: string) => {
    if (!address) return "";
    try {
      const addr = Address.parse(address);
      const formatted = addr.toString();
      return `${formatted.slice(0, 6)}...${formatted.slice(-6)}`;
    } catch {
      return `${address.slice(0, 6)}...${address.slice(-6)}`;
    }
  };

  // Action button component for DRY
  const ActionButton = ({
    icon: Icon,
    onClick,
  }: {
    icon: typeof Plus;
    onClick?: () => void;
  }) => (
    <Button
      size="icon"
      variant="default"
      className={BUTTON_CLASSES.actionButton}
      onClick={onClick}
    >
      <Icon className="w-4 h-4 stroke-[2.5]" />
    </Button>
  );

  return (
    <header className="bg-ton-black text-white p-2">
      <div className="flex items-center justify-between gap-1 min-w-0">
        <div className="flex items-center gap-1 bg-ton-black/60 min-w-0 flex-1">
          {/* Balance Section */}
          <div className="flex items-center gap-1 min-w-0 flex-shrink">
            <TonLogo size={20} className="w-5 h-5 flex-shrink-0" />
            <div className="flex items-center gap-1 min-w-0">
              <span className="text-sm font-bold truncate">
                {balanceInfo.available}
              </span>
              {balanceInfo.hasLocked && (
                <span className="text-[10px] text-gray-300 flex items-center gap-1 flex-shrink-0">
                  (<Lock className="w-2 h-2" />
                  {balanceInfo.locked.toFixed(2)})
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            <ActionButton icon={Plus} />
            <ActionButton icon={Minus} />
          </div>

          {/* Profile Button */}
          <div
            onClick={onProfileButtonClick}
            className={cn(
              "w-6 h-6 cursor-pointer rounded-full overflow-hidden bg-gray-300 flex items-center justify-center",
              isLoading && "pointer-events-none"
            )}
          >
            {currentUser?.photoURL ? (
              <img
                src={currentUser.photoURL}
                alt="User avatar"
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="w-3 h-3 text-gray-600" />
            )}
          </div>
        </div>

        {/* Wallet Button */}
        <Button
          variant="default"
          size="sm"
          className={cn(BUTTON_CLASSES.walletButton, "gap-1")}
          onClick={handleWalletAction}
          disabled={isConnecting}
        >
          <TonLogo size={12} className="w-4 h-4" />
          <span className="ml-0.5 text-xs truncate">
            {isConnecting
              ? "Connecting..."
              : tonWalletAddress
              ? formatAddress(tonWalletAddress)
              : "Connect"}
          </span>
        </Button>
      </div>
    </header>
  );
}
