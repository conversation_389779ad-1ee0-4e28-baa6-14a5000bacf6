import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import { Api } from "telegram/tl";
import dotenv from "dotenv";

dotenv.config();

interface GiftMessage {
  id: number;
  senderId: string;
  senderName: string;
  gift: any;
  message?: string;
  date: Date;
}

class TelegramUserClient {
  private client: TelegramClient | null = null;
  private apiId: number;
  private apiHash: string;
  private sessionString: string;
  private phoneNumber: string;
  private isConnected: boolean = false;

  constructor() {
    this.apiId = parseInt(process.env.TELEGRAM_API_ID || "0");
    this.apiHash = process.env.TELEGRAM_API_HASH || "";
    this.sessionString = process.env.TELEGRAM_SESSION_STRING || "";
    this.phoneNumber = process.env.TELEGRAM_PHONE_NUMBER || "";

    if (!this.apiId || !this.apiHash) {
      throw new Error("TELEGRAM_API_ID and TELEGRAM_API_HASH are required");
    }
  }

  /**
   * Initialize and connect the Telegram user client
   */
  async connect(): Promise<void> {
    try {
      const session = new StringSession(this.sessionString);
      this.client = new TelegramClient(session, this.apiId, this.apiHash, {
        connectionRetries: 5,
      });

      console.log("Connecting to Telegram user account...");

      await this.client.start({
        phoneNumber: async () => this.phoneNumber,
        password: async () => {
          // If 2FA is enabled, you'll need to handle password input
          console.log("2FA password required - implement password input");
          return "";
        },
        phoneCode: async () => {
          // In production, you'd need a way to input the verification code
          console.log(
            "Phone verification code required - implement code input"
          );
          return "";
        },
        onError: (err) => console.error("Auth error:", err),
      });

      this.isConnected = true;
      console.log("Connected to Telegram user account successfully");

      // Save session string for future use
      try {
        const newSessionString =
          this.client.session.save() as unknown as string;
        if (newSessionString && newSessionString !== this.sessionString) {
          console.log("New session string:", newSessionString);
          console.log(
            "Please update TELEGRAM_SESSION_STRING in your .env file"
          );
        }
      } catch (error) {
        console.log("Could not save session string:", error);
      }

      // Set up message handlers
      this.setupMessageHandlers();
    } catch (error) {
      console.error("Failed to connect to Telegram user account:", error);
      throw error;
    }
  }

  /**
   * Set up message handlers to listen for incoming gifts
   */
  private setupMessageHandlers(): void {
    if (!this.client) return;

    this.client.addEventHandler(async (event: any) => {
      try {
        if (event.message && event.message.action) {
          // Check if this is a star gift message
          if (event.message.action.className === "MessageActionStarGift") {
            await this.handleReceivedGift(event.message);
          }
        }
      } catch (error) {
        console.error("Error handling message event:", error);
      }
    });

    console.log("Message handlers set up for gift detection");
  }

  /**
   * Handle received star gifts
   */
  private async handleReceivedGift(message: any): Promise<void> {
    try {
      console.log("Received a star gift!");

      const giftAction = message.action;
      const senderId = message.fromId?.userId?.toString();
      const gift = giftAction.gift;
      const giftMessage = giftAction.message;

      if (!senderId) {
        console.log("Could not determine sender ID");
        return;
      }

      console.log(`Gift received from user ${senderId}:`, {
        giftId: gift.id?.toString(),
        stars: gift.stars?.toString(),
        message: giftMessage?.text || "No message",
        date: new Date(message.date * 1000),
      });

      // Return the gift to sender
      await this.returnGiftToSender(senderId, gift);
    } catch (error) {
      console.error("Error handling received gift:", error);
    }
  }

  /**
   * Return a gift to the sender
   */
  private async returnGiftToSender(
    senderId: string,
    originalGift: any
  ): Promise<void> {
    try {
      if (!this.client) {
        throw new Error("Client not connected");
      }

      console.log(`Returning gift to user ${senderId}...`);

      // Get available star gifts
      const starGifts = await this.client.invoke(
        new Api.payments.GetStarGifts({ hash: 0 })
      );

      if (
        !starGifts ||
        !("gifts" in starGifts) ||
        starGifts.gifts.length === 0
      ) {
        console.log("No star gifts available to send");
        return;
      }

      // Find a suitable gift to return (preferably the same one or similar value)
      let giftToSend = starGifts.gifts.find(
        (gift: any) => gift.id?.toString() === originalGift.id?.toString()
      );

      // If exact gift not available, find one with similar star value
      if (!giftToSend) {
        const originalStars = parseInt(originalGift.stars?.toString() || "0");
        giftToSend = starGifts.gifts.find(
          (gift: any) =>
            parseInt(gift.stars?.toString() || "0") <= originalStars
        );
      }

      // If still no suitable gift, use the cheapest available
      if (!giftToSend) {
        giftToSend = starGifts.gifts.reduce((cheapest: any, current: any) => {
          const cheapestStars = parseInt(
            cheapest.stars?.toString() || "999999"
          );
          const currentStars = parseInt(current.stars?.toString() || "999999");
          return currentStars < cheapestStars ? current : cheapest;
        });
      }

      if (!giftToSend) {
        console.log("No suitable gift found to return");
        return;
      }

      // For now, just send a thank you message since gift API is complex
      // In a real implementation, you'd need to handle the full payment flow
      console.log(
        `Would return gift to user ${senderId}, sending thank you instead`
      );
      await this.sendThankYouMessage(senderId);
    } catch (error) {
      console.error("Error returning gift to sender:", error);

      // If gift sending fails, at least send a thank you message
      try {
        await this.sendThankYouMessage(senderId);
      } catch (msgError) {
        console.error("Failed to send thank you message:", msgError);
      }
    }
  }

  /**
   * Send a thank you message if gift return fails
   */
  private async sendThankYouMessage(senderId: string): Promise<void> {
    try {
      if (!this.client) return;

      // Send message to user by ID (simplified approach)
      await this.client.sendMessage(parseInt(senderId), {
        message: "Thank you so much for the gift! 🎁❤️ I really appreciate it!",
      });

      console.log(`Sent thank you message to user ${senderId}`);
    } catch (error) {
      console.error("Error sending thank you message:", error);
    }
  }

  /**
   * Get user's received star gifts
   */
  async getUserStarGifts(limit: number = 10): Promise<GiftMessage[]> {
    try {
      if (!this.client) {
        throw new Error("Client not connected");
      }

      // For now, return empty array since the API is complex
      // In a real implementation, you'd need to properly handle the star gifts API
      console.log(
        `Getting user star gifts (limit: ${limit}) - not implemented yet`
      );
      return [];
    } catch (error) {
      console.error("Error getting user star gifts:", error);
      return [];
    }
  }

  /**
   * Disconnect the client
   */
  async disconnect(): Promise<void> {
    if (this.client && this.isConnected) {
      await this.client.disconnect();
      this.isConnected = false;
      console.log("Disconnected from Telegram user account");
    }
  }

  /**
   * Check if client is connected
   */
  isClientConnected(): boolean {
    return this.isConnected && this.client !== null;
  }
}

export default TelegramUserClient;
export { GiftMessage };
