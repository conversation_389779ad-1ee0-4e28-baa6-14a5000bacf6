import TelegramUser<PERSON>lient, { GiftMessage } from "./telegram-user-client";
import { Telegraf } from "telegraf";

interface GiftStats {
  totalReceived: number;
  totalReturned: number;
  totalFailed: number;
  lastGiftDate?: Date;
}

class GiftManager {
  private userClient: TelegramUserClient;
  private bot: Telegraf;
  private stats: GiftStats = {
    totalReceived: 0,
    totalReturned: 0,
    totalFailed: 0,
  };
  private isRunning: boolean = false;

  constructor(bot: Telegraf) {
    this.userClient = new TelegramUserClient();
    this.bot = bot;
  }

  /**
   * Initialize the gift manager
   */
  async initialize(): Promise<void> {
    try {
      console.log("Initializing Gift Manager...");
      
      // Connect the user client
      await this.userClient.connect();
      
      this.isRunning = true;
      console.log("Gift Manager initialized successfully");
      
      // Set up periodic status checks
      this.startStatusMonitoring();
    } catch (error) {
      console.error("Failed to initialize Gift Manager:", error);
      throw error;
    }
  }

  /**
   * Start monitoring gift manager status
   */
  private startStatusMonitoring(): void {
    // Check status every 5 minutes
    setInterval(async () => {
      if (!this.userClient.isClientConnected()) {
        console.log("User client disconnected, attempting to reconnect...");
        try {
          await this.userClient.connect();
        } catch (error) {
          console.error("Failed to reconnect user client:", error);
        }
      }
    }, 5 * 60 * 1000);

    // Log stats every hour
    setInterval(() => {
      this.logStats();
    }, 60 * 60 * 1000);
  }

  /**
   * Get gift statistics
   */
  getStats(): GiftStats {
    return { ...this.stats };
  }

  /**
   * Log current statistics
   */
  private logStats(): void {
    console.log("Gift Manager Stats:", {
      totalReceived: this.stats.totalReceived,
      totalReturned: this.stats.totalReturned,
      totalFailed: this.stats.totalFailed,
      successRate: this.stats.totalReceived > 0 
        ? ((this.stats.totalReturned / this.stats.totalReceived) * 100).toFixed(2) + "%"
        : "N/A",
      lastGiftDate: this.stats.lastGiftDate?.toISOString() || "Never",
    });
  }

  /**
   * Update statistics when a gift is received
   */
  updateStatsOnReceived(): void {
    this.stats.totalReceived++;
    this.stats.lastGiftDate = new Date();
  }

  /**
   * Update statistics when a gift is successfully returned
   */
  updateStatsOnReturned(): void {
    this.stats.totalReturned++;
  }

  /**
   * Update statistics when gift return fails
   */
  updateStatsOnFailed(): void {
    this.stats.totalFailed++;
  }

  /**
   * Get recent received gifts
   */
  async getRecentGifts(limit: number = 10): Promise<GiftMessage[]> {
    try {
      return await this.userClient.getUserStarGifts(limit);
    } catch (error) {
      console.error("Error getting recent gifts:", error);
      return [];
    }
  }

  /**
   * Send gift status to admin users via bot
   */
  async sendStatusToAdmin(chatId: number): Promise<void> {
    try {
      const stats = this.getStats();
      const isConnected = this.userClient.isClientConnected();
      
      const statusMessage = `
🎁 **Gift Manager Status**

**Connection:** ${isConnected ? "✅ Connected" : "❌ Disconnected"}
**Total Gifts Received:** ${stats.totalReceived}
**Total Gifts Returned:** ${stats.totalReturned}
**Total Failed Returns:** ${stats.totalFailed}
**Success Rate:** ${stats.totalReceived > 0 
  ? ((stats.totalReturned / stats.totalReceived) * 100).toFixed(2) + "%"
  : "N/A"}
**Last Gift:** ${stats.lastGiftDate?.toLocaleString() || "Never"}

**Status:** ${this.isRunning ? "🟢 Running" : "🔴 Stopped"}
      `.trim();

      await this.bot.telegram.sendMessage(chatId, statusMessage, {
        parse_mode: "Markdown",
      });
    } catch (error) {
      console.error("Error sending status to admin:", error);
    }
  }

  /**
   * Get recent gifts and send to admin
   */
  async sendRecentGiftsToAdmin(chatId: number, limit: number = 5): Promise<void> {
    try {
      const gifts = await this.getRecentGifts(limit);
       
      if (gifts.length === 0) {
        await this.bot.telegram.sendMessage(chatId, "No recent gifts found.");
        return;
      }

      let message = "🎁 **Recent Gifts Received:**\n\n";
      
      gifts.forEach((gift, index) => {
        message += `**${index + 1}.** From: ${gift.senderName || gift.senderId}\n`;
        message += `   Date: ${gift.date.toLocaleString()}\n`;
        if (gift.message) {
          message += `   Message: "${gift.message}"\n`;
        }
        message += "\n";
      });

      await this.bot.telegram.sendMessage(chatId, message, {
        parse_mode: "Markdown",
      });
    } catch (error) {
      console.error("Error sending recent gifts to admin:", error);
    }
  }

  /**
   * Manually trigger reconnection
   */
  async reconnect(): Promise<boolean> {
    try {
      console.log("Manually triggering reconnection...");
      await this.userClient.disconnect();
      await this.userClient.connect();
      return true;
    } catch (error) {
      console.error("Manual reconnection failed:", error);
      return false;
    }
  }

  /**
   * Stop the gift manager
   */
  async stop(): Promise<void> {
    try {
      console.log("Stopping Gift Manager...");
      this.isRunning = false;
      await this.userClient.disconnect();
      console.log("Gift Manager stopped");
    } catch (error) {
      console.error("Error stopping Gift Manager:", error);
    }
  }

  /**
   * Check if gift manager is running
   */
  isGiftManagerRunning(): boolean {
    return this.isRunning && this.userClient.isClientConnected();
  }
}

export default GiftManager;
export { GiftStats };
