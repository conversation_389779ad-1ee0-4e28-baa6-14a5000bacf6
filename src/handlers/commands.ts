import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";

export const handleStartCommand = (ctx: Context) => {
  const welcomeMessage = `
🛍️ Welcome to the Marketplace Bot!

This bot helps you access our marketplace platform. Use the buttons below to get started or open the full marketplace using the menu button.
  `;

  ctx.reply(welcomeMessage, createMainKeyboard());
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(
    "🤖 Marketplace Bot Help\n\n" +
      "**Basic Commands:**\n" +
      "/start - Start the bot and show main menu\n" +
      "/help - Show this help message\n\n" +
      "**Available Buttons:**\n" +
      "📋 Get My Orders - View and manage your orders\n" +
      "✅ Complete Order - Get help with completing orders\n" +
      "🎁 Echo Gift - Send a gift to the bot and get it echoed back\n" +
      "🔗 Get Referral Link - Get your referral link\n\n" +
      "**Gift Manager Commands (Admin):**\n" +
      "/gift_start - Start automatic gift return system\n" +
      "/gift_stop - Stop automatic gift return system\n" +
      "/gift_status - Check gift manager status\n" +
      "/gift_recent - View recent received gifts\n" +
      "/gift_reconnect - Reconnect to Telegram account\n" +
      "/gift_help - Show detailed gift manager help\n\n" +
      "**Order Completion Flow:**\n" +
      "1. Use 'Get My Orders' to see orders ready for completion\n" +
      "2. Click on a paid order to start completion\n" +
      "3. Send the gift/item to this bot\n" +
      "4. The bot will automatically complete the purchase\n\n" +
      "**Gift Return System:**\n" +
      "The bot can automatically return gifts sent to a connected Telegram user account. " +
      "When someone sends a star gift, it will automatically send one back!\n\n" +
      "You can also use the menu button to open the full marketplace web app.",
    createMarketplaceInlineKeyboard()
  );
};
