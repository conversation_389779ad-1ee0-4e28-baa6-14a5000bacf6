import { Context } from "telegraf";
import GiftManager from "../services/gift-manager";

let giftManager: GiftManager | null = null;

/**
 * Initialize gift manager with bot instance
 */
export function initializeGiftManager(bot: any): void {
  giftManager = new GiftManager(bot);
}

/**
 * Start the gift manager
 */
export async function handleStartGiftManager(ctx: Context): Promise<void> {
  try {
    // Check if user is admin (you might want to implement proper admin check)
    const userId = ctx.from?.id;
    if (!userId) {
      await ctx.reply("Unable to identify user.");
      return;
    }

    // For now, allow any user to start gift manager
    // In production, you should check if user is admin
    
    if (!giftManager) {
      await ctx.reply("Gift manager not initialized. Please contact administrator.");
      return;
    }

    if (giftManager.isGiftManagerRunning()) {
      await ctx.reply("🎁 Gift manager is already running!");
      return;
    }

    await ctx.reply("🔄 Starting gift manager...");
    
    try {
      await giftManager.initialize();
      await ctx.reply("✅ Gift manager started successfully! Now listening for incoming gifts.");
    } catch (error) {
      console.error("Failed to start gift manager:", error);
      await ctx.reply("❌ Failed to start gift manager. Please check the logs and ensure Telegram user account is properly configured.");
    }
  } catch (error) {
    console.error("Error in handleStartGiftManager:", error);
    await ctx.reply("An error occurred while starting the gift manager.");
  }
}

/**
 * Stop the gift manager
 */
export async function handleStopGiftManager(ctx: Context): Promise<void> {
  try {
    if (!giftManager) {
      await ctx.reply("Gift manager not initialized.");
      return;
    }

    if (!giftManager.isGiftManagerRunning()) {
      await ctx.reply("🎁 Gift manager is not running.");
      return;
    }

    await ctx.reply("🔄 Stopping gift manager...");
    
    await giftManager.stop();
    await ctx.reply("✅ Gift manager stopped successfully.");
  } catch (error) {
    console.error("Error in handleStopGiftManager:", error);
    await ctx.reply("An error occurred while stopping the gift manager.");
  }
}

/**
 * Get gift manager status
 */
export async function handleGiftStatus(ctx: Context): Promise<void> {
  try {
    if (!giftManager) {
      await ctx.reply("Gift manager not initialized.");
      return;
    }

    const chatId = ctx.chat?.id;
    if (!chatId) {
      await ctx.reply("Unable to determine chat ID.");
      return;
    }

    await giftManager.sendStatusToAdmin(chatId);
  } catch (error) {
    console.error("Error in handleGiftStatus:", error);
    await ctx.reply("An error occurred while getting gift status.");
  }
}

/**
 * Get recent gifts
 */
export async function handleRecentGifts(ctx: Context): Promise<void> {
  try {
    if (!giftManager) {
      await ctx.reply("Gift manager not initialized.");
      return;
    }

    const chatId = ctx.chat?.id;
    if (!chatId) {
      await ctx.reply("Unable to determine chat ID.");
      return;
    }

    await giftManager.sendRecentGiftsToAdmin(chatId, 10);
  } catch (error) {
    console.error("Error in handleRecentGifts:", error);
    await ctx.reply("An error occurred while getting recent gifts.");
  }
}

/**
 * Reconnect gift manager
 */
export async function handleReconnectGiftManager(ctx: Context): Promise<void> {
  try {
    if (!giftManager) {
      await ctx.reply("Gift manager not initialized.");
      return;
    }

    await ctx.reply("🔄 Attempting to reconnect gift manager...");
    
    const success = await giftManager.reconnect();
    
    if (success) {
      await ctx.reply("✅ Gift manager reconnected successfully!");
    } else {
      await ctx.reply("❌ Failed to reconnect gift manager. Please check the logs.");
    }
  } catch (error) {
    console.error("Error in handleReconnectGiftManager:", error);
    await ctx.reply("An error occurred while reconnecting the gift manager.");
  }
}

/**
 * Show gift manager help
 */
export async function handleGiftHelp(ctx: Context): Promise<void> {
  try {
    const helpMessage = `
🎁 **Gift Manager Commands**

**/gift_start** - Start the gift manager
**/gift_stop** - Stop the gift manager  
**/gift_status** - Show current status and statistics
**/gift_recent** - Show recent received gifts
**/gift_reconnect** - Reconnect to Telegram user account
**/gift_help** - Show this help message

**How it works:**
1. The bot connects to a Telegram user account
2. When someone sends a star gift to that account, it automatically returns a gift
3. All gift activity is logged and tracked
4. Admins can monitor status and statistics

**Setup Requirements:**
- TELEGRAM_API_ID and TELEGRAM_API_HASH in environment
- TELEGRAM_PHONE_NUMBER for the user account
- TELEGRAM_SESSION_STRING (generated on first login)

**Note:** Make sure the user account has sufficient stars to return gifts!
    `.trim();

    await ctx.reply(helpMessage, { parse_mode: "Markdown" });
  } catch (error) {
    console.error("Error in handleGiftHelp:", error);
    await ctx.reply("An error occurred while showing gift help.");
  }
}

/**
 * Get the gift manager instance (for use in other parts of the application)
 */
export function getGiftManager(): GiftManager | null {
  return giftManager;
}
