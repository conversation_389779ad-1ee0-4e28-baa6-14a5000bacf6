import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import readline from "readline";
import dotenv from "dotenv";

dotenv.config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

async function setupTelegramAccount() {
  console.log("🔧 Telegram User Account Setup");
  console.log("===============================");
  console.log();

  const apiId = parseInt(process.env.TELEGRAM_API_ID || "0");
  const apiHash = process.env.TELEGRAM_API_HASH || "";

  if (!apiId || !apiHash) {
    console.error("❌ TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in .env file");
    console.log("You can get these from https://my.telegram.org/apps");
    process.exit(1);
  }

  console.log("✅ API credentials found");
  console.log(`API ID: ${apiId}`);
  console.log(`API Hash: ${apiHash.substring(0, 8)}...`);
  console.log();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    console.log("🔄 Connecting to Telegram...");
    
    await client.start({
      phoneNumber: async () => {
        return new Promise((resolve) => {
          rl.question("📱 Please enter your phone number (with country code, e.g., +**********): ", (phone) => {
            resolve(phone);
          });
        });
      },
      password: async () => {
        return new Promise((resolve) => {
          rl.question("🔐 Please enter your 2FA password (if enabled): ", (password) => {
            resolve(password);
          });
        });
      },
      phoneCode: async () => {
        return new Promise((resolve) => {
          rl.question("📨 Please enter the verification code you received: ", (code) => {
            resolve(code);
          });
        });
      },
      onError: (err) => {
        console.error("❌ Authentication error:", err);
      },
    });

    console.log();
    console.log("✅ Successfully connected to Telegram!");
    
    const sessionString = client.session.save();
    console.log();
    console.log("🔑 Your session string:");
    console.log("========================");
    console.log(sessionString);
    console.log("========================");
    console.log();
    console.log("📝 Please add this to your .env file as:");
    console.log(`TELEGRAM_SESSION_STRING=${sessionString}`);
    console.log();
    console.log("⚠️  IMPORTANT: Keep this session string secure!");
    console.log("   - Don't share it with anyone");
    console.log("   - Don't commit it to version control");
    console.log("   - Store it safely in your .env file");
    console.log();

    // Test sending a message to self
    try {
      await client.sendMessage("me", { 
        message: "🎁 Gift Manager Setup Complete! This account is now ready to receive and return gifts." 
      });
      console.log("✅ Test message sent successfully!");
    } catch (error) {
      console.log("⚠️  Warning: Could not send test message:", error);
    }

    await client.disconnect();
    console.log("✅ Setup complete! You can now start the gift manager.");
    
  } catch (error) {
    console.error("❌ Setup failed:", error);
    console.log();
    console.log("🔧 Troubleshooting:");
    console.log("1. Make sure your API ID and API Hash are correct");
    console.log("2. Check your internet connection");
    console.log("3. Verify your phone number format (+country_code_number)");
    console.log("4. Make sure you entered the verification code correctly");
    console.log("5. If you have 2FA enabled, make sure you entered the correct password");
  } finally {
    rl.close();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupTelegramAccount().catch(console.error);
}

export default setupTelegramAccount;
