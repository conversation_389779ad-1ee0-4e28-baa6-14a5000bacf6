"use client";

import { miniApp, useLaunchParams, useSignal } from "@telegram-apps/sdk-react";
import { AppRoot } from "@telegram-apps/telegram-ui";
import { TonConnectUIProvider } from "@tonconnect/ui-react";
import { type PropsWithChildren, useEffect, useState } from "react";

import { ErrorBoundary } from "@/components/ErrorBoundary";
import { ErrorPage } from "@/components/ErrorPage";
import { useDidMount } from "@/hooks/useDidMount";
import { handleReferralFromUrl } from "@/utils/referral-utils";

function TelegramRootInner({ children }: PropsWithChildren) {
  // This component assumes Telegram SDK is available
  const lp = useLaunchParams();
  const isDark = useSignal(miniApp.isDark);

  return (
    <TonConnectUIProvider manifestUrl="http://*************:3001/tonconnect-manifest.json?v=3">
      <AppRoot
        appearance={isDark ? "dark" : "light"}
        platform={
          ["macos", "ios"].includes(lp.tgWebAppPlatform) ? "ios" : "base"
        }
      >
        {children}
      </AppRoot>
    </TonConnectUIProvider>
  );
}

function FallbackRootInner({ children }: PropsWithChildren) {
  // This component is used when Telegram SDK is not available
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Use system preference for dark mode
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    setIsDarkMode(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => setIsDarkMode(e.matches);
    mediaQuery.addEventListener("change", handleChange);

    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  return (
    <TonConnectUIProvider manifestUrl="/tonconnect-manifest.json">
      <AppRoot appearance={isDarkMode ? "dark" : "light"} platform="base">
        {children}
      </AppRoot>
    </TonConnectUIProvider>
  );
}

function RootInner({ children }: PropsWithChildren) {
  const [telegramAvailable, setTelegramAvailable] = useState<boolean | null>(
    null
  );

  useEffect(() => {
    // Check if we're on admin or auth pages
    const pathname = window.location.pathname;
    const isAdminOrAuth =
      pathname.startsWith("/admin") || pathname.startsWith("/auth");

    if (isAdminOrAuth && process.env.NODE_ENV === "production") {
      // For admin/auth pages in production, don't use Telegram
      setTelegramAvailable(false);
      return;
    }

    // Try to detect if Telegram SDK is available
    try {
      // This is a simple check that doesn't call hooks
      if (typeof window !== "undefined" && window.Telegram?.WebApp) {
        setTelegramAvailable(true);
      } else {
        setTelegramAvailable(false);
      }
    } catch {
      setTelegramAvailable(false);
    }
  }, []);

  // Show loading while determining Telegram availability
  if (telegramAvailable === null) {
    return <div className="root__loading">Loading...</div>;
  }

  // Use appropriate component based on Telegram availability
  if (telegramAvailable) {
    return <TelegramRootInner>{children}</TelegramRootInner>;
  } else {
    return <FallbackRootInner>{children}</FallbackRootInner>;
  }
}

export function Root(props: PropsWithChildren) {
  // Unfortunately, Telegram Mini Apps does not allow us to use all features of
  // the Server Side Rendering. That's why we are showing loader on the server
  // side.
  const didMount = useDidMount();

  // Handle referral links when the app loads
  useEffect(() => {
    if (didMount) {
      handleReferralFromUrl();
    }
  }, [didMount]);

  return didMount ? (
    <ErrorBoundary fallback={ErrorPage}>
      <RootInner {...props} />
    </ErrorBoundary>
  ) : (
    <div className="root__loading">Loading</div>
  );
}
