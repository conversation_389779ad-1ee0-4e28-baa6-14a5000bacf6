# Marketplace Bot

A simple Telegram bot for the marketplace platform that handles order management and gift echo functionality.

## Features

- **Order Management**: View and complete marketplace orders
- **Gift Echo**: Simple gift echo system using Telegram Bot API transferGift method
- **Referral System**: Generate and share referral links
- **Business Account Integration**: Uses Telegram Business Account for gift transfers

## Setup

1. **Install Dependencies**

   ```bash
   npm install
   ```

2. **Environment Configuration**

   Create a `.env` file with the following variables:

   ```env
   # Bot Configuration
   BOT_TOKEN=your_bot_token_here
   ```

3. **Get Bot Token**

   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Create a new bot with `/newbot`
   - Copy the bot token to your `.env` file

4. **Set Up Business Account**

   - Connect your bot to a Telegram Business Account
   - The business connection ID will be automatically provided in gift messages

5. **Start the Bot**
   ```bash
   npm run dev
   ```

## Bot Commands

### User Commands

- `/start` - Start the bot and show main menu
- `/help` - Show help information

## How It Works

### Order Management

1. Users can view their marketplace orders using "📋 Get My Orders"
2. For paid orders, users can complete them by sending the item/gift to the bot
3. The bot automatically processes the completion and updates the marketplace

### Gift Echo System

1. User sends a gift directly to the business account (not to the bot)
2. Bot receives a `business_message` update containing the gift information
3. The bot logs the gift data (sender, gift ID, business connection ID, timestamp)
4. The bot immediately transfers the same gift back to the sender using the transferGift API
5. Simple and efficient - uses official Bot API business account integration

### Referral System

Users can generate referral links to earn commissions on marketplace transactions.

## Architecture

- **Bot Handler** (`src/bot.ts`): Main bot configuration with simple gift echo logic
- **Command Handlers** (`src/handlers/`): Handle different types of user interactions
- **Utilities** (`src/utils/`): Keyboard layouts and helper functions

## Development

- **Development Mode**: `npm run dev` (with auto-reload)
- **Build**: `npm run build`
- **Production**: `npm start`

## Gift Echo Implementation

The gift echo functionality is implemented using a middleware to catch business message updates:

```typescript
bot.use(async (ctx, next) => {
  try {
    // Check if this is a business message update with a gift
    if (ctx.update && "business_message" in ctx.update) {
      const message = (ctx.update as any).business_message;

      if (message && typeof message === "object" && "gift" in message) {
        const gift = (message as any).gift;
        const senderId = (message as any).from?.id;
        const businessConnectionId = (message as any).business_connection_id;

        console.log("Gift received in business account:", {
          giftId: gift?.id,
          senderId,
          businessConnectionId,
          date: new Date(),
        });

        if (senderId && gift?.id && businessConnectionId) {
          await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/transferGift`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              business_connection_id: businessConnectionId,
              owned_gift_id: gift.id,
              new_owner_chat_id: senderId,
            }),
          });

          console.log(`Gift echoed back to user ${senderId}`);
        }
        return; // Don't continue to next middleware for gift messages
      }
    }

    await next(); // Continue for non-gift messages
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
});
```

## Security Notes

- Keep your bot token secure
- Ensure proper business account permissions
- Monitor gift transfer activity through logs

## Troubleshooting

### Common Issues

1. **Gift transfers not working**:

   - Verify the bot is connected to a Telegram Business Account
   - Check that the business account has the necessary permissions
   - Ensure the bot receives `business_message` updates (check webhook/polling settings)
   - Verify the business account owns the gift being transferred

2. **Bot not responding**:
   - Check if the bot token is correct
   - Verify the bot is running
   - Check console logs for errors
