# Marketplace Bot

A simple Telegram bot for the marketplace platform that handles order management and gift echo functionality.

## Features

- **Order Management**: View and complete marketplace orders
- **Gift Echo**: Simple gift echo system using Telegram Bot API transferGift method
- **Referral System**: Generate and share referral links
- **Business Account Integration**: Uses Telegram Business Account for gift transfers

## Setup

1. **Install Dependencies**

   ```bash
   npm install
   ```

2. **Environment Configuration**

   Create a `.env` file with the following variables:

   ```env
   # Bot Configuration
   BOT_TOKEN=your_bot_token_here

   # Business Account Configuration
   BUSINESS_CONNECTION_ID=your_business_connection_id
   ```

3. **Get Bot Token**

   - Message [@BotFather](https://t.me/botfather) on Telegram
   - Create a new bot with `/newbot`
   - Copy the bot token to your `.env` file

4. **Set Up Business Account**

   - Connect your bot to a Telegram Business Account
   - Get the business connection ID
   - Add it to your `.env` file

5. **Start the Bot**
   ```bash
   npm run dev
   ```

## Bot Commands

### User Commands

- `/start` - Start the bot and show main menu
- `/help` - Show help information

## How It Works

### Order Management

1. Users can view their marketplace orders using "📋 Get My Orders"
2. For paid orders, users can complete them by sending the item/gift to the bot
3. The bot automatically processes the completion and updates the marketplace

### Gift Echo System

1. When someone sends a gift to the bot's business account, it's automatically detected
2. The bot logs the gift data (sender, gift ID, timestamp)
3. The bot immediately transfers the same gift back to the sender using the transferGift API
4. Simple and efficient - no complex user account management needed

### Referral System

Users can generate referral links to earn commissions on marketplace transactions.

## Architecture

- **Bot Handler** (`src/bot.ts`): Main bot configuration with simple gift echo logic
- **Command Handlers** (`src/handlers/`): Handle different types of user interactions
- **Utilities** (`src/utils/`): Keyboard layouts and helper functions

## Development

- **Development Mode**: `npm run dev` (with auto-reload)
- **Build**: `npm run build`
- **Production**: `npm start`

## Gift Echo Implementation

The gift echo functionality is implemented directly in the main bot handler:

```typescript
bot.on("message", async (ctx) => {
  if (ctx.message && "gift" in ctx.message) {
    const gift = (ctx.message as any).gift;
    const senderId = ctx.from?.id;

    console.log("Gift received:", {
      giftId: gift?.id,
      senderId,
      date: new Date(),
    });

    if (senderId && gift?.id) {
      await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/transferGift`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          business_connection_id: process.env.BUSINESS_CONNECTION_ID,
          owned_gift_id: gift.id,
          new_owner_chat_id: senderId,
        }),
      });

      console.log(`Gift echoed back to user ${senderId}`);
    }
  }
});
```

## Security Notes

- Keep your bot token secure
- Ensure proper business account permissions
- Monitor gift transfer activity through logs

## Troubleshooting

### Common Issues

1. **Gift transfers not working**:

   - Verify business connection ID is correct
   - Check bot has proper business account permissions
   - Ensure the business account owns the gift being transferred

2. **Bot not responding**:
   - Check if the bot token is correct
   - Verify the bot is running
   - Check console logs for errors
